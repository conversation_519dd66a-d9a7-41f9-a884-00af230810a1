import React, { useEffect, useRef, useCallback, useImperativeHandle, forwardRef } from 'react';
import Sketch from 'react-p5';
import p5Types from 'p5';
import { useRobotAnimation, RobotState } from '@/hooks/use-robot-animation';

interface RobotAnimationProps {
  className?: string;
  onRobotStateChange?: (state: RobotState) => void;
  isVisible?: boolean;
  isGlidingAway?: boolean;
  isFullScreenMode?: boolean;
}

export interface RobotAnimationRef {
  setRobotState: (state: RobotState) => void;
  setRobotScale: (scale: number) => void;
  centerRobot: () => void;
  rollToPosition: (x: number, y: number) => void;
  glideAway: () => void;
  setInitialPosition: (x: number, y: number) => void;
}

// Constants
const TRANSITION_DURATION = 500;
const VIEW_TRANSITION_DURATION = 300;
const SHADOW_OPACITY = 0.3;
const SHADOW_SCALE_Y = 0.2;
const SHADOW_OFFSET_Y = 20;
const HOLD_THRESHOLD = 300;
const MAX_SPIN_SPEED = 0.15;
const SCALE_TRANSITION_SPEED = 0.1;
const POSITION_TRANSITION_SPEED = 0.05;

const RobotAnimation = forwardRef<RobotAnimationRef, RobotAnimationProps>(({
  className = '',
  onRobotStateChange,
  isVisible = true,
  isGlidingAway = false,
  isFullScreenMode = false
}, ref) => {
  const {
    state,
    updateState,
    setRobotState,
    setRobotScale,
    centerRobot,
    rollToPosition,
    initializePosition,
    setPosition,
  } = useRobotAnimation();

  const robotImagesRef = useRef<{
    front: p5Types.Image | null;
    side: p5Types.Image | null;
    threeQuarter: p5Types.Image | null;
    back: p5Types.Image | null;
    top: p5Types.Image | null;
  }>({
    front: null,
    side: null,
    threeQuarter: null,
    back: null,
    top: null,
  });

  const p5InstanceRef = useRef<p5Types | null>(null);

  // Helper function to check if mouse is over UI elements
  const isMouseOverUI = useCallback((p5: p5Types) => {
    // Header area (top of screen)
    const headerHeight = 60;
    if (p5.mouseY <= headerHeight) {
      return true;
    }

    // Search bar area (center-top area where search interface is located)
    const searchAreaTop = headerHeight;
    const searchAreaBottom = p5.height * 0.4; // Top 40% of screen below header
    const searchAreaLeft = p5.width * 0.1;    // 10% margin from left
    const searchAreaRight = p5.width * 0.9;   // 10% margin from right

    if (p5.mouseX >= searchAreaLeft && p5.mouseX <= searchAreaRight &&
        p5.mouseY >= searchAreaTop && p5.mouseY <= searchAreaBottom) {
      return true;
    }

    // Robot controls area
    const controlsLeft = 16;
    const controlsRight = 216;
    const controlsTop = (p5.height / 2) - 200;
    const controlsBottom = (p5.height / 2) + 200;

    // Go Away button area
    const goAwayButtonLeft = 16;
    const goAwayButtonRight = 150;
    const goAwayButtonTop = p5.height - 70;
    const goAwayButtonBottom = p5.height - 16;

    // Daswos button area (bottom-left)
    const daswosButtonLeft = 16;
    const daswosButtonRight = 150;
    const daswosButtonTop = p5.height - 120;
    const daswosButtonBottom = p5.height - 80;

    // Check if mouse is over robot controls
    if (p5.mouseX >= controlsLeft && p5.mouseX <= controlsRight &&
        p5.mouseY >= controlsTop && p5.mouseY <= controlsBottom) {
      return true;
    }

    // Check if mouse is over go away button
    if (p5.mouseX >= goAwayButtonLeft && p5.mouseX <= goAwayButtonRight &&
        p5.mouseY >= goAwayButtonTop && p5.mouseY <= goAwayButtonBottom) {
      return true;
    }

    // Check if mouse is over daswos button area
    if (p5.mouseX >= daswosButtonLeft && p5.mouseX <= daswosButtonRight &&
        p5.mouseY >= daswosButtonTop && p5.mouseY <= daswosButtonBottom) {
      return true;
    }

    return false;
  }, []);

  // Glide away function
  const glideAway = useCallback(() => {
    if (p5InstanceRef.current) {
      const p5 = p5InstanceRef.current;
      // Set target position to off-screen right
      const targetX = p5.width + 200; // Off-screen to the right
      const targetY = state.robotY; // Keep same Y position

      // Set robot to side view for gliding, but keep it in idle state (no rolling)
      updateState({
        targetView: 'side',
        currentView: 'side',
        targetX,
        targetY,
        isRolling: false, // Don't roll - just glide smoothly
      });

      // Keep robot in idle state for the gliding animation
      setRobotState('idle');
    }
  }, [state.robotY, updateState, setRobotState]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    setRobotState,
    setRobotScale,
    centerRobot,
    rollToPosition,
    glideAway,
    setInitialPosition: (x: number, y: number) => {
      // Use initializePosition to set both current position and center
      if (initializePosition) {
        initializePosition(x, y);
      }
    },
  }), [setRobotState, setRobotScale, centerRobot, rollToPosition, glideAway, initializePosition]);

  // Notify parent of state changes
  useEffect(() => {
    if (onRobotStateChange) {
      onRobotStateChange(state.robotState);
    }
  }, [state.robotState, onRobotStateChange]);

  const preload = useCallback((p5: p5Types) => {
    // Load robot images
    robotImagesRef.current.front = p5.loadImage('/assets/robot/robot_front_view.png');
    robotImagesRef.current.side = p5.loadImage('/assets/robot/robot_side_view.png');
    robotImagesRef.current.threeQuarter = p5.loadImage('/assets/robot/robot_three_quarter_view.png');
    robotImagesRef.current.back = p5.loadImage('/assets/robot/robot_back_view.png');
    robotImagesRef.current.top = p5.loadImage('/assets/robot/robot_top_view.png');
  }, []);

  const setup = useCallback((p5: p5Types, canvasParentRef: Element) => {
    p5InstanceRef.current = p5;

    // Create canvas that fills the window
    p5.createCanvas(p5.windowWidth, p5.windowHeight).parent(canvasParentRef);

    // Initialize robot position in compact mode (bottom-right corner)
    const compactX = p5.width * 0.85; // 85% from left (bottom-right area)
    const compactY = p5.height * 0.75; // 75% from top (lower area)
    initializePosition(compactX, compactY);

    // Start in idle state (no entrance animation)
    setRobotState('idle');
  }, [initializePosition, setRobotState]);

  const updateAnimation = useCallback((p5: p5Types) => {
    const currentTime = p5.millis();
    const timeInState = currentTime - state.stateStartTime;

    // If gliding away, handle special gliding animation
    if (isGlidingAway) {
      // Force side view and smooth gliding movement (no rolling)
      const dx = state.targetX - state.robotX;
      const dy = state.targetY - state.robotY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance > 5) {
        const glideSpeed = 8; // Fast speed for gliding away
        const newX = state.robotX + Math.cos(Math.atan2(dy, dx)) * glideSpeed;
        const newY = state.robotY + Math.sin(Math.atan2(dy, dx)) * glideSpeed;

        updateState({
          robotX: newX,
          robotY: newY,
          targetView: 'side',
          currentView: 'side',
          isRolling: false, // No rolling - just smooth gliding
          bodyRotation: 0, // Keep robot upright
          bodyRotationSpeed: 0,
        });
      }
      return; // Skip normal animation logic when gliding away
    }

    // Handle smooth scale transitions
    if (Math.abs(state.robotScale - state.targetScale) > 0.01) {
      const newScale = p5.lerp(state.robotScale, state.targetScale, SCALE_TRANSITION_SPEED);
      updateState({ robotScale: newScale });
    }

    // Handle smooth return to center when needed
    if (state.shouldReturnToCenter && !state.isRolling) {
      const distanceToCenter = p5.dist(state.robotX, state.robotY, state.centerX, state.centerY);
      if (distanceToCenter > 2) {
        const newX = p5.lerp(state.robotX, state.centerX, POSITION_TRANSITION_SPEED);
        const newY = p5.lerp(state.robotY, state.centerY, POSITION_TRANSITION_SPEED);
        updateState({ robotX: newX, robotY: newY });
      } else {
        updateState({
          robotX: state.centerX,
          robotY: state.centerY,
          shouldReturnToCenter: false,
        });
      }
    }

    // Handle mouse hold spinning
    if (state.isMousePressed) {
      const holdDuration = currentTime - state.mouseHoldStartTime;
      if (holdDuration > HOLD_THRESHOLD && !state.isSpinning) {
        updateState({ isSpinning: true, spinSpeed: 0 });
      }

      if (state.isSpinning) {
        const newSpinSpeed = Math.min(state.spinSpeed + 0.005, MAX_SPIN_SPEED);
        const newSpinRotation = state.spinRotation + newSpinSpeed;
        updateState({ spinSpeed: newSpinSpeed, spinRotation: newSpinRotation });
      }
    } else if (state.isSpinning) {
      const newSpinSpeed = Math.max(state.spinSpeed - 0.01, 0);
      const newSpinRotation = state.spinRotation + newSpinSpeed;

      if (newSpinSpeed <= 0) {
        updateState({ isSpinning: false, spinSpeed: 0, spinRotation: 0 });
      } else {
        updateState({ spinSpeed: newSpinSpeed, spinRotation: newSpinRotation });
      }
    }

    // Handle rolling movement
    if (state.isRolling) {
      const dx = state.targetX - state.robotX;
      const dy = state.targetY - state.robotY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < 5) {
        updateState({ isRolling: false });
        if (state.robotState === 'roll') {
          setRobotState('idle');
        }
      } else {
        const rollDirection = Math.atan2(dy, dx);
        const rollSpeed = Math.min(distance * 0.05, 5);
        const newX = state.robotX + Math.cos(rollDirection) * rollSpeed;
        const newY = state.robotY + Math.sin(rollDirection) * rollSpeed;

        const bodyRotationSpeed = rollSpeed * 0.2;
        const newBodyRotation = state.bodyRotation + bodyRotationSpeed;

        // Set appropriate view based on roll direction
        let targetView = state.targetView;
        if (Math.abs(Math.cos(rollDirection)) > Math.abs(Math.sin(rollDirection))) {
          targetView = 'side';
        } else {
          targetView = Math.sin(rollDirection) > 0 ? 'threeQuarter' : 'back';
        }

        updateState({
          robotX: newX,
          robotY: newY,
          rollDirection,
          rollSpeed,
          bodyRotation: newBodyRotation,
          bodyRotationSpeed,
          targetView,
          legsVisible: false,
          legsVisibility: Math.max(0, state.legsVisibility - 0.1),
        });
      }
    } else if (state.robotState !== 'roll' && !state.legsVisible) {
      updateState({
        legsVisible: true,
        legsVisibility: Math.min(1, state.legsVisibility + 0.05),
      });
    }

    // Handle view transitions
    if (state.currentView !== state.targetView) {
      const newProgress = Math.min(1, timeInState / VIEW_TRANSITION_DURATION);
      if (newProgress >= 1) {
        updateState({
          currentView: state.targetView,
          viewTransitionProgress: 0,
        });
      } else {
        updateState({ viewTransitionProgress: newProgress });
      }
    }

    // State-specific updates
    let stateUpdates: any = {};

    switch (state.robotState) {
      case 'idle':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.002) * 5,
          headRotation: Math.sin(currentTime * 0.001) * 0.1,
          armLeftRotation: Math.sin(currentTime * 0.001) * 0.05,
          armRightRotation: Math.sin(currentTime * 0.001 + Math.PI) * 0.05,
          targetView: state.isRolling ? state.targetView : 'front',
        };

        // Handle eye blinking
        if (currentTime > state.eyeBlinkTime && !state.isBlinking) {
          stateUpdates.isBlinking = true;
          stateUpdates.eyeBlinkTime = currentTime + 200;
        } else if (currentTime > state.eyeBlinkTime && state.isBlinking) {
          stateUpdates.isBlinking = false;
          stateUpdates.eyeBlinkTime = currentTime + p5.random(2000, 5000);
        }
        break;

      case 'talk':
        const newMouthAnimation = state.mouthAnimation + 0.15;
        stateUpdates = {
          talkPulse: Math.sin(currentTime * 0.01) * 0.05,
          headBobAmount: Math.sin(currentTime * 0.01) * 3,
          armLeftRotation: Math.sin(currentTime * 0.008) * 0.2,
          armRightRotation: Math.sin(currentTime * 0.008 + Math.PI) * 0.2,
          targetView: 'front',
          mouthAnimation: newMouthAnimation,
          mouthOpenAmount: Math.abs(Math.sin(newMouthAnimation)) * 0.8, // Mouth opens and closes
        };
        break;

      case 'dance':
        const newDancePhase = state.dancePhase + 0.05;
        stateUpdates = {
          dancePhase: newDancePhase,
          headBobAmount: Math.sin(newDancePhase * 2) * 8,
          headRotation: Math.sin(newDancePhase) * 0.2,
          armLeftRotation: Math.sin(newDancePhase) * 0.4,
          armRightRotation: Math.sin(newDancePhase + Math.PI) * 0.4,
        };

        if (!state.isRolling) {
          stateUpdates.robotX = state.danceStartX + Math.sin(newDancePhase) * 30;
          stateUpdates.robotY = state.danceStartY + Math.sin(newDancePhase * 0.5) * 10;
        }

        // Cycle through views for dancing
        if (timeInState % 2000 < 500) {
          stateUpdates.targetView = 'front';
        } else if (timeInState % 2000 < 1000) {
          stateUpdates.targetView = 'threeQuarter';
        } else if (timeInState % 2000 < 1500) {
          stateUpdates.targetView = 'side';
        } else {
          stateUpdates.targetView = 'threeQuarter';
        }
        break;

      case 'search':
        const newSearchAngle = state.searchAngle + 0.03;
        stateUpdates = {
          searchAngle: newSearchAngle,
          headRotation: Math.sin(newSearchAngle) * 0.3,
          headBobAmount: Math.sin(currentTime * 0.005) * 3,
          armLeftRotation: Math.sin(newSearchAngle * 0.5) * 0.2,
          armRightRotation: Math.sin(newSearchAngle * 0.5 + Math.PI) * 0.2,
        };

        // Cycle through views for searching
        if (timeInState % 3000 < 1000) {
          stateUpdates.targetView = 'front';
        } else if (timeInState % 3000 < 2000) {
          stateUpdates.targetView = 'threeQuarter';
        } else {
          stateUpdates.targetView = 'side';
        }
        break;

      case 'roll':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.01) * 3,
          armLeftRotation: Math.sin(currentTime * 0.01) * 0.1 * (state.rollSpeed * 0.1),
          armRightRotation: Math.sin(currentTime * 0.01 + Math.PI) * 0.1 * (state.rollSpeed * 0.1),
        };
        break;
    }

    if (Object.keys(stateUpdates).length > 0) {
      updateState(stateUpdates);
    }
  }, [state, updateState, setRobotState, isGlidingAway]);

  const drawShadow = useCallback((p5: p5Types) => {
    p5.push();
    p5.translate(0, SHADOW_OFFSET_Y);
    p5.fill(0, 0, 0, SHADOW_OPACITY * 255);
    p5.noStroke();
    p5.ellipse(0, 0, 120 * state.robotScale, 30 * state.robotScale * SHADOW_SCALE_Y);
    p5.pop();
  }, [state.robotScale]);

  const drawRobot = useCallback((p5: p5Types) => {
    p5.push();
    p5.translate(state.robotX, state.robotY);

    // Draw shadow
    drawShadow(p5);

    // Apply bobbing effect
    p5.translate(0, state.headBobAmount);

    // Scale the robot
    p5.scale(state.robotScale);

    // Apply spin rotation if spinning
    if (state.isSpinning || state.spinSpeed > 0) {
      p5.rotate(state.spinRotation);
    }

    // Apply body rotation for wheel effect (only when actually rolling, not when gliding away)
    if (state.robotState === 'roll' && !isGlidingAway) {
      p5.push();
      p5.rotate(state.bodyRotation);
    }

    // Determine which image to draw based on current view and transition
    const currentImage = robotImagesRef.current[state.currentView];

    // If transitioning between views, blend them
    if (state.currentView !== state.targetView && state.viewTransitionProgress > 0) {
      // Draw current view with fading opacity
      p5.tint(255, 255, 255, 255 * (1 - state.viewTransitionProgress));
      if (currentImage) {
        p5.image(currentImage, -currentImage.width / 2, -currentImage.height / 2);
      }

      // Draw target view with increasing opacity
      p5.tint(255, 255, 255, 255 * state.viewTransitionProgress);
      const targetImage = robotImagesRef.current[state.targetView];
      if (targetImage) {
        p5.image(targetImage, -targetImage.width / 2, -targetImage.height / 2);
      }

      // Reset tint
      p5.noTint();
    } else {
      // Apply effects based on state
      if (state.robotState === 'talk') {
        // Subtle pulsing for talking
        p5.scale(1 + state.talkPulse);
      }

      // Draw the current view
      if (currentImage) {
        p5.image(currentImage, -currentImage.width / 2, -currentImage.height / 2);
      }

      // Draw mouth when talking (only on front view)
      if (state.robotState === 'talk' && state.currentView === 'front') {
        p5.push();
        p5.fill(20, 20, 20); // Very dark color for mouth
        p5.noStroke();

        // Position mouth much higher and make it much bigger
        const mouthY = -10; // Move up significantly on the robot's face
        const mouthWidth = 60 + state.mouthOpenAmount * 40; // Much larger base width
        const mouthHeight = 25 + state.mouthOpenAmount * 35; // Much larger height

        p5.ellipse(0, mouthY, mouthWidth, mouthHeight);
        p5.pop();
      }
    }

    if (state.robotState === 'roll' && !isGlidingAway) {
      p5.pop(); // End body rotation
    }

    p5.pop();
  }, [state, drawShadow]);

  const handleMouseInteraction = useCallback((p5: p5Types) => {
    // Don't handle mouse interactions when gliding away
    if (isGlidingAway) {
      return;
    }

    // Only handle mouse interactions in full screen mode
    if (!isFullScreenMode) {
      return;
    }

    // Don't handle mouse interactions when cursor is over UI elements
    if (isMouseOverUI(p5)) {
      return;
    }

    // Calculate distance from mouse to robot
    const d = p5.dist(p5.mouseX, p5.mouseY, state.robotX, state.robotY);

    // If mouse is near robot and moving (scale interaction range with robot size)
    if (d < 150 * state.robotScale &&
        (Math.abs(p5.mouseX - state.lastMouseX) > 5 || Math.abs(p5.mouseY - state.lastMouseY) > 5)) {

      // Make robot look toward mouse
      const angle = Math.atan2(p5.mouseY - state.robotY, p5.mouseX - state.robotX);
      const newHeadRotation = p5.lerp(state.headRotation, angle * 0.2, 0.1);

      // Set appropriate view based on mouse position
      let targetView = state.targetView;
      if (Math.abs(Math.cos(angle)) > 0.7) {
        // Mouse is more to the sides
        targetView = 'threeQuarter';
      } else {
        // Mouse is more above/below
        targetView = Math.sin(angle) > 0 ? 'front' : 'top';
      }

      updateState({
        headRotation: newHeadRotation,
        targetView,
        mouseInteractionTimer: p5.millis() + 1000,
      });
    }

    // If interaction timer expired, return to default view for current state
    if (state.mouseInteractionTimer > 0 && p5.millis() > state.mouseInteractionTimer) {
      let defaultView = 'front';
      switch (state.robotState) {
        case 'idle':
        case 'talk':
          defaultView = 'front';
          break;
        case 'dance':
        case 'search':
        case 'roll':
          // Keep current view for these states
          return;
      }

      updateState({
        mouseInteractionTimer: 0,
        targetView: defaultView,
      });
    }

    // Store current mouse position for next frame
    updateState({
      lastMouseX: p5.mouseX,
      lastMouseY: p5.mouseY,
    });
  }, [state, updateState, isGlidingAway, isMouseOverUI, isFullScreenMode]);

  const draw = useCallback((p5: p5Types) => {
    // Clear background - make it transparent
    p5.clear();

    // Update animation
    updateAnimation(p5);

    // Draw robot
    drawRobot(p5);

    // Handle mouse interaction
    handleMouseInteraction(p5);
  }, [updateAnimation, drawRobot, handleMouseInteraction]);

  const mousePressed = useCallback((p5: p5Types) => {
    // Don't handle mouse press when cursor is over UI elements
    if (isMouseOverUI(p5)) {
      return;
    }

    const d = p5.dist(p5.mouseX, p5.mouseY, state.robotX, state.robotY);

    updateState({
      isMousePressed: true,
      mouseHoldStartTime: p5.millis(),
    });

    if (d < 100 * state.robotScale) {
      // Quick reaction animation (will trigger if not held long enough)
      setTimeout(() => {
        if (!state.isSpinning && !state.isMousePressed) {
          updateState({ headBobAmount: -10 });
          setTimeout(() => {
            updateState({ headBobAmount: 0 });
          }, 300);
        }
      }, HOLD_THRESHOLD + 50);
    }
  }, [state, updateState, isMouseOverUI]);

  const mouseReleased = useCallback((p5: p5Types) => {
    const holdDuration = p5.millis() - state.mouseHoldStartTime;

    updateState({ isMousePressed: false });

    // Don't handle mouse release when cursor is over UI elements
    if (isMouseOverUI(p5)) {
      return;
    }

    // If it was a quick click (not a hold), handle normal click behavior
    if (holdDuration < HOLD_THRESHOLD && !state.isSpinning) {
      const d = p5.dist(p5.mouseX, p5.mouseY, state.robotX, state.robotY);
      if (d >= 100 * state.robotScale) {
        // Roll to where user clicked
        rollToPosition(p5.mouseX, p5.mouseY);
      }
    }
  }, [state, updateState, rollToPosition, isMouseOverUI]);

  const windowResized = useCallback((p5: p5Types) => {
    p5.resizeCanvas(p5.windowWidth, p5.windowHeight);

    // Update center position
    const newCenterX = p5.width / 2;
    const newCenterY = p5.height / 2;
    updateState({
      centerX: newCenterX,
      centerY: newCenterY,
    });
  }, [updateState]);

  // Don't render anything if not visible
  if (!isVisible) {
    return null;
  }

  return (
    <div className={`fixed inset-0 ${isFullScreenMode ? 'pointer-events-auto' : 'pointer-events-none'} ${className}`} style={{ zIndex: isFullScreenMode ? 50 : 10 }}>
      <div className={isFullScreenMode ? 'pointer-events-auto' : 'pointer-events-none'}>
        <Sketch
          preload={preload}
          setup={setup}
          draw={draw}
          mousePressed={isFullScreenMode ? mousePressed : undefined}
          mouseReleased={isFullScreenMode ? mouseReleased : undefined}
          windowResized={windowResized}
        />
      </div>
    </div>
  );
});

RobotAnimation.displayName = 'RobotAnimation';

export default RobotAnimation;

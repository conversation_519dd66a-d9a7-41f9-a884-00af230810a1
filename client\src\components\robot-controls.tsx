import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import {
  Bot,
  MessageCircle,
  Music,
  RotateCw,
  Search,
  Target,
  RotateCcw,
  Minus,
  Plus,
  X,
  Maximize,
  Minimize,
  Mic,
  Footprints,
  <PERSON>rkles,
  RotateCcw as FlipIcon,
  Zap
} from 'lucide-react';
import { RobotState } from '@/hooks/use-robot-animation';

interface RobotControlsProps {
  robotState: RobotState;
  robotScale: number;
  onStateChange: (state: RobotState) => void;
  onScaleChange: (scale: number) => void;
  onCenter: () => void;
  onRoll: () => void;
  onClose?: () => void;
  onFullScreenToggle?: () => void;
  isFullScreenMode?: boolean;
  className?: string;
}

const RobotControls: React.FC<RobotControlsProps> = ({
  robotState,
  robotScale,
  onStateChange,
  onScaleChange,
  onCenter,
  onRoll,
  onClose,
  onFullScreenToggle,
  isFullScreenMode = false,
  className = '',
}) => {
  const scalePercentage = Math.round(robotScale * 100);

  const handleScaleUp = () => {
    onScaleChange(Math.min(1.5, robotScale + 0.1));
  };

  const handleScaleDown = () => {
    onScaleChange(Math.max(0.2, robotScale - 0.1));
  };

  const handleResetScale = () => {
    onScaleChange(0.5);
  };

  return (
    <div className={`fixed left-4 top-1/2 transform -translate-y-1/2 z-50 ${className}`}>
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg">
        <div className="flex flex-col space-y-3">
          {/* Header with title and close button */}
          <div className="flex items-center justify-between mb-2">
            <div className="text-xs font-medium text-gray-600 dark:text-gray-400">
              Robot Controls
            </div>
            {onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Close robot controls"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>

          <Button
            variant={robotState === 'idle' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('idle')}
            className="w-full justify-start"
          >
            <Bot className="h-4 w-4 mr-2" />
            Idle
          </Button>

          <Button
            variant={robotState === 'talk' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('talk')}
            className="w-full justify-start"
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Talk
          </Button>

          <Button
            variant={robotState === 'dance' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('dance')}
            className="w-full justify-start"
          >
            <Music className="h-4 w-4 mr-2" />
            Dance
          </Button>

          <Button
            variant={robotState === 'search' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('search')}
            className="w-full justify-start"
          >
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>

          <Button
            variant={robotState === 'sing' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('sing')}
            className="w-full justify-start"
          >
            <Mic className="h-4 w-4 mr-2" />
            Sing
          </Button>

          <Button
            variant={robotState === 'walk' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('walk')}
            className="w-full justify-start"
          >
            <Footprints className="h-4 w-4 mr-2" />
            Walk
          </Button>

          <Button
            variant={robotState === 'glamour' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('glamour')}
            className="w-full justify-start"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Glamour
          </Button>

          <Button
            variant={robotState === 'back' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('back')}
            className="w-full justify-start"
          >
            <FlipIcon className="h-4 w-4 mr-2" />
            Back View
          </Button>

          <Button
            variant={robotState === 'trainers' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onStateChange('trainers')}
            className="w-full justify-start"
          >
            <Zap className="h-4 w-4 mr-2" />
            Trainers
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onRoll}
            className="w-full justify-start"
          >
            <RotateCw className="h-4 w-4 mr-2" />
            Roll
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCenter}
            className="w-full justify-start"
          >
            <Target className="h-4 w-4 mr-2" />
            Center
          </Button>

          {onFullScreenToggle && (
            <Button
              variant={isFullScreenMode ? 'default' : 'outline'}
              size="sm"
              onClick={onFullScreenToggle}
              className="w-full justify-start"
              title={isFullScreenMode ? 'Exit full screen mode' : 'Enter full screen mode'}
            >
              {isFullScreenMode ? (
                <Minimize className="h-4 w-4 mr-2" />
              ) : (
                <Maximize className="h-4 w-4 mr-2" />
              )}
              {isFullScreenMode ? 'Compact' : 'Full Screen'}
            </Button>
          )}

          {/* Scale Controls */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
            <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
              Size: {scalePercentage}%
            </div>

            <div className="flex items-center space-x-2 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleScaleDown}
                className="p-1 h-8 w-8"
              >
                <Minus className="h-3 w-3" />
              </Button>

              <div className="flex-1">
                <Slider
                  value={[robotScale]}
                  onValueChange={(value) => onScaleChange(value[0])}
                  min={0.2}
                  max={1.5}
                  step={0.1}
                  className="w-full"
                />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleScaleUp}
                className="p-1 h-8 w-8"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleResetScale}
              className="w-full text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Reset
            </Button>
          </div>

          {/* Current State Indicator */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Status: <span className="font-medium capitalize">{robotState}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RobotControls;

import React, { useState, useCallback, useRef } from 'react';
import RobotAnimation, { RobotAnimationRef } from './robot-animation';
import RobotControls from './robot-controls';
import DaswosButton from './daswos-button';
import GoAwayButton from './go-away-button';
import { RobotState } from '@/hooks/use-robot-animation';
import { useRobotContext } from '@/contexts/robot-context';

interface RobotOverlayProps {
  className?: string;
}

const RobotOverlay: React.FC<RobotOverlayProps> = ({ className = '' }) => {
  const [robotState, setRobotState] = useState<RobotState>('idle');
  const [robotScale, setRobotScale] = useState<number>(0.5);
  const [isRobotActive, setIsRobotActive] = useState<boolean>(false);
  const [isRobotGlidingAway, setIsRobotGlidingAway] = useState<boolean>(false);
  const [showRobotControls, setShowRobotControls] = useState<boolean>(true);
  const [isFullScreenMode, setIsFullScreenMode] = useState<boolean>(false);
  const robotAnimationRef = useRef<RobotAnimationRef>(null);

  // Use robot context to share full screen state globally
  const { setIsRobotFullScreen } = useRobotContext();

  const handleStateChange = useCallback((newState: RobotState) => {
    setRobotState(newState);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotState(newState);
    }
  }, []);

  const handleScaleChange = useCallback((newScale: number) => {
    setRobotScale(newScale);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotScale(newScale);
    }
  }, []);

  const handleCenter = useCallback(() => {
    if (robotAnimationRef.current) {
      robotAnimationRef.current.centerRobot();
    }
  }, []);

  const handleRoll = useCallback(() => {
    // Roll to a random position
    const x = Math.random() * window.innerWidth * 0.6 + window.innerWidth * 0.2;
    const y = Math.random() * window.innerHeight * 0.6 + window.innerHeight * 0.2;

    if (robotAnimationRef.current) {
      robotAnimationRef.current.rollToPosition(x, y);
    }
    setRobotState('roll');
  }, []);

  const handleRobotStateChange = useCallback((state: RobotState) => {
    setRobotState(state);
  }, []);

  const handleDaswosButtonClick = useCallback(() => {
    setIsRobotActive(true);
    setShowRobotControls(true); // Show controls when robot is activated
    setIsFullScreenMode(false); // Start in compact mode

    // Position robot in bottom-right corner (compact mode) with a small delay to ensure setup is complete
    setTimeout(() => {
      if (robotAnimationRef.current) {
        const targetX = window.innerWidth * 0.85; // 85% from left (bottom-right area)
        const targetY = window.innerHeight * 0.75; // 75% from top (lower area)
        robotAnimationRef.current.setInitialPosition(targetX, targetY);
      }
    }, 100); // Small delay to ensure robot animation is initialized
  }, []);

  const handleCloseRobotControls = useCallback(() => {
    setShowRobotControls(false);
  }, []);

  const handleFullScreenToggle = useCallback(() => {
    const newFullScreenMode = !isFullScreenMode;
    setIsFullScreenMode(newFullScreenMode);
    setIsRobotFullScreen(newFullScreenMode); // Update global context

    if (robotAnimationRef.current) {
      if (newFullScreenMode) {
        // Move to center for full screen mode
        const targetX = window.innerWidth * 0.5;  // Center horizontally
        const targetY = window.innerHeight * 0.5; // Center vertically
        robotAnimationRef.current.setInitialPosition(targetX, targetY);
      } else {
        // Move back to bottom-right for compact mode
        const targetX = window.innerWidth * 0.85; // 85% from left
        const targetY = window.innerHeight * 0.75; // 75% from top
        robotAnimationRef.current.setInitialPosition(targetX, targetY);
      }
    }
  }, [isFullScreenMode, setIsRobotFullScreen]);

  const handleCloseRobot = useCallback(() => {
    setIsRobotActive(false);
    setShowRobotControls(true); // Reset controls visibility for next time
  }, []);

  const handleGoAway = useCallback(() => {
    setIsRobotGlidingAway(true);
    setIsFullScreenMode(false); // Reset to compact mode
    setIsRobotFullScreen(false); // Reset global context
    if (robotAnimationRef.current) {
      robotAnimationRef.current.glideAway();
    }

    // After the glide animation completes, hide everything
    setTimeout(() => {
      setIsRobotActive(false);
      setIsRobotGlidingAway(false);
    }, 2000); // 2 seconds for the glide animation
  }, [setIsRobotFullScreen]);

  return (
    <div className={className}>
      {/* Show only the Daswos button when robot is not active */}
      {!isRobotActive && (
        <DaswosButton onClick={handleDaswosButtonClick} />
      )}

      {/* Show full robot interface when active */}
      {isRobotActive && (
        <>
          {/* Robot Animation Layer */}
          <RobotAnimation
            ref={robotAnimationRef}
            onRobotStateChange={handleRobotStateChange}
            isVisible={isRobotActive}
            isGlidingAway={isRobotGlidingAway}
            isFullScreenMode={isFullScreenMode}
          />

          {/* Robot Controls - hide when gliding away or when controls are closed */}
          {!isRobotGlidingAway && showRobotControls && (
            <RobotControls
              robotState={robotState}
              robotScale={robotScale}
              onStateChange={handleStateChange}
              onScaleChange={handleScaleChange}
              onCenter={handleCenter}
              onRoll={handleRoll}
              onClose={handleCloseRobotControls}
              onFullScreenToggle={handleFullScreenToggle}
              isFullScreenMode={isFullScreenMode}
            />
          )}

          {/* Go Away Button - show when robot is active but not gliding */}
          {!isRobotGlidingAway && (
            <GoAwayButton onClick={handleGoAway} />
          )}
        </>
      )}
    </div>
  );
};

export default RobotOverlay;

import React, { useState, useCallback, useRef } from 'react';
import RobotAnimation, { RobotAnimationRef } from './robot-animation';
import RobotControls from './robot-controls';
import DaswosButton from './daswos-button';
import GoAwayButton from './go-away-button';
import { RobotState } from '@/hooks/use-robot-animation';

interface RobotOverlayProps {
  className?: string;
}

const RobotOverlay: React.FC<RobotOverlayProps> = ({ className = '' }) => {
  const [robotState, setRobotState] = useState<RobotState>('idle');
  const [robotScale, setRobotScale] = useState<number>(0.5);
  const [isRobotActive, setIsRobotActive] = useState<boolean>(false);
  const [isRobotGlidingAway, setIsRobotGlidingAway] = useState<boolean>(false);
  const [showRobotControls, setShowRobotControls] = useState<boolean>(true);
  const robotAnimationRef = useRef<RobotAnimationRef>(null);

  const handleStateChange = useCallback((newState: RobotState) => {
    setRobotState(newState);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotState(newState);
    }
  }, []);

  const handleScaleChange = useCallback((newScale: number) => {
    setRobotScale(newScale);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotScale(newScale);
    }
  }, []);

  const handleCenter = useCallback(() => {
    if (robotAnimationRef.current) {
      robotAnimationRef.current.centerRobot();
    }
  }, []);

  const handleRoll = useCallback(() => {
    // Roll to a random position
    const x = Math.random() * window.innerWidth * 0.6 + window.innerWidth * 0.2;
    const y = Math.random() * window.innerHeight * 0.6 + window.innerHeight * 0.2;

    if (robotAnimationRef.current) {
      robotAnimationRef.current.rollToPosition(x, y);
    }
    setRobotState('roll');
  }, []);

  const handleRobotStateChange = useCallback((state: RobotState) => {
    setRobotState(state);
  }, []);

  const handleDaswosButtonClick = useCallback(() => {
    setIsRobotActive(true);
    setShowRobotControls(true); // Show controls when robot is activated

    // Position robot at specific location (lower-right area as shown in screenshot)
    if (robotAnimationRef.current) {
      const targetX = window.innerWidth * 0.75; // 75% from left
      const targetY = window.innerHeight * 0.7;  // 70% from top
      robotAnimationRef.current.setInitialPosition(targetX, targetY);
    }
  }, []);

  const handleCloseRobotControls = useCallback(() => {
    setShowRobotControls(false);
  }, []);

  const handleCloseRobot = useCallback(() => {
    setIsRobotActive(false);
    setShowRobotControls(true); // Reset controls visibility for next time
  }, []);

  const handleGoAway = useCallback(() => {
    setIsRobotGlidingAway(true);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.glideAway();
    }

    // After the glide animation completes, hide everything
    setTimeout(() => {
      setIsRobotActive(false);
      setIsRobotGlidingAway(false);
    }, 2000); // 2 seconds for the glide animation
  }, []);

  return (
    <div className={className}>
      {/* Show only the Daswos button when robot is not active */}
      {!isRobotActive && (
        <DaswosButton onClick={handleDaswosButtonClick} />
      )}

      {/* Show full robot interface when active */}
      {isRobotActive && (
        <>
          {/* Robot Animation Layer */}
          <RobotAnimation
            ref={robotAnimationRef}
            onRobotStateChange={handleRobotStateChange}
            isVisible={isRobotActive}
            isGlidingAway={isRobotGlidingAway}
          />

          {/* Robot Controls - hide when gliding away or when controls are closed */}
          {!isRobotGlidingAway && showRobotControls && (
            <RobotControls
              robotState={robotState}
              robotScale={robotScale}
              onStateChange={handleStateChange}
              onScaleChange={handleScaleChange}
              onCenter={handleCenter}
              onRoll={handleRoll}
              onClose={handleCloseRobotControls}
            />
          )}

          {/* Go Away Button - show when robot is active but not gliding */}
          {!isRobotGlidingAway && (
            <GoAwayButton onClick={handleGoAway} />
          )}
        </>
      )}
    </div>
  );
};

export default RobotOverlay;

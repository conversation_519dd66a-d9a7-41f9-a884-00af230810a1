import React, { useState, useCallback, useRef, useEffect } from 'react';
import RobotAnimation, { RobotAnimationRef } from './robot-animation';
import RobotControls from './robot-controls';
import DaswosButton from './daswos-button';
import GoAwayButton from './go-away-button';
import VoiceControlManager from './voice-control-manager';
import FullscreenProductDisplay from './fullscreen-product-display';
import { RobotState } from '@/hooks/use-robot-animation';
import { useRobotContext } from '@/contexts/robot-context';
import { useToast } from '@/hooks/use-toast';

interface RobotOverlayProps {
  className?: string;
}

const RobotOverlay: React.FC<RobotOverlayProps> = ({ className = '' }) => {
  const [robotState, setRobotState] = useState<RobotState>('idle');
  const [robotScale, setRobotScale] = useState<number>(0.5);
  const [isRobotActive, setIsRobotActive] = useState<boolean>(false);
  const [isRobotGlidingAway, setIsRobotGlidingAway] = useState<boolean>(false);
  const [showRobotControls, setShowRobotControls] = useState<boolean>(true);
  const [isFullScreenMode, setIsFullScreenMode] = useState<boolean>(false);
  const [isVoiceListening, setIsVoiceListening] = useState<boolean>(false);
  const [voiceStatus, setVoiceStatus] = useState<string>('');
  const [showProductDisplay, setShowProductDisplay] = useState<boolean>(false);
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('');
  const robotAnimationRef = useRef<RobotAnimationRef>(null);

  // Use robot context to share full screen state globally
  const { setIsRobotFullScreen } = useRobotContext();
  const { toast } = useToast();

  const handleStateChange = useCallback((newState: RobotState) => {
    setRobotState(newState);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotState(newState);
    }
  }, []);

  const handleScaleChange = useCallback((newScale: number) => {
    setRobotScale(newScale);
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotScale(newScale);
    }
  }, []);

  const handleCenter = useCallback(() => {
    if (robotAnimationRef.current) {
      robotAnimationRef.current.centerRobot();
    }
  }, []);

  const handleRoll = useCallback(() => {
    // Roll to a random position
    const x = Math.random() * window.innerWidth * 0.6 + window.innerWidth * 0.2;
    const y = Math.random() * window.innerHeight * 0.6 + window.innerHeight * 0.2;

    if (robotAnimationRef.current) {
      robotAnimationRef.current.rollToPosition(x, y);
    }
    setRobotState('roll');
  }, []);

  const handleRobotStateChange = useCallback((state: RobotState) => {
    setRobotState(state);
  }, []);

  const handleDaswosButtonClick = useCallback(() => {
    setIsRobotActive(true);
    setShowRobotControls(true); // Show controls when robot is activated
    setIsFullScreenMode(false); // Start in compact mode
    setIsRobotFullScreen(false); // Always reset global context to compact mode
    setIsVoiceListening(true); // Start voice listening when robot is activated

    // Ensure robot is in compact position immediately (robot animation already initializes there)
    // Only set position if we need to move from a different position
    if (robotAnimationRef.current) {
      const targetX = window.innerWidth * 0.92; // 92% from left (further right to avoid nav buttons)
      const targetY = window.innerHeight * 0.82; // 82% from top (lower to avoid nav buttons)
      robotAnimationRef.current.setInitialPosition(targetX, targetY);
    }

    // Show activation toast
    toast({
      title: 'Daswos Robot Activated',
      description: 'Voice commands are now active. Say "Daswos" followed by your command.',
    });
  }, [setIsRobotFullScreen, toast]);

  const handleCloseRobotControls = useCallback(() => {
    setShowRobotControls(false);
  }, []);

  const handleFullScreenToggle = useCallback(() => {
    const newFullScreenMode = !isFullScreenMode;
    setIsFullScreenMode(newFullScreenMode);
    setIsRobotFullScreen(newFullScreenMode); // Update global context

    if (robotAnimationRef.current) {
      if (newFullScreenMode) {
        // Move to center for full screen mode
        const targetX = window.innerWidth * 0.5;  // Center horizontally
        const targetY = window.innerHeight * 0.5; // Center vertically
        robotAnimationRef.current.setInitialPosition(targetX, targetY);
      } else {
        // Move back to bottom-right for compact mode
        const targetX = window.innerWidth * 0.92; // 92% from left (further right to avoid nav buttons)
        const targetY = window.innerHeight * 0.82; // 82% from top (lower to avoid nav buttons)
        robotAnimationRef.current.setInitialPosition(targetX, targetY);
      }
    }
  }, [isFullScreenMode, setIsRobotFullScreen]);

  const handleCloseRobot = useCallback(() => {
    setIsRobotActive(false);
    setShowRobotControls(true); // Reset controls visibility for next time
    setIsVoiceListening(false); // Stop voice listening
    setVoiceStatus(''); // Clear voice status
  }, []);

  const handleGoAway = useCallback(() => {
    setIsRobotGlidingAway(true);
    setIsFullScreenMode(false); // Reset to compact mode
    setIsRobotFullScreen(false); // Reset global context
    setIsVoiceListening(false); // Stop voice listening
    setVoiceStatus(''); // Clear voice status

    if (robotAnimationRef.current) {
      robotAnimationRef.current.glideAway();
    }

    // After the glide animation completes, hide everything
    setTimeout(() => {
      setIsRobotActive(false);
      setIsRobotGlidingAway(false);
    }, 2000); // 2 seconds for the glide animation
  }, [setIsRobotFullScreen]);

  const handleCloseProductDisplay = useCallback(() => {
    setShowProductDisplay(false);
    setCurrentSearchQuery('');
  }, []);

  const handleVoiceCommand = useCallback((command: string) => {
    console.log('🗣️ Voice command received:', command);

    // Update robot state to show it's processing
    if (robotAnimationRef.current) {
      robotAnimationRef.current.setRobotState('talk');
    }

    // Show command received toast
    toast({
      title: 'Voice Command Received',
      description: `"${command}"`,
    });
  }, [toast]);

  // Voice event listeners
  useEffect(() => {
    const handleVoiceStatus = (event: CustomEvent) => {
      const { status, message } = event.detail;
      setVoiceStatus(message || '');

      // Update robot state based on voice status
      if (status === 'listening' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('search');
      } else if (status === 'speaking' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('talk');
      } else if (status === 'idle' && robotAnimationRef.current) {
        robotAnimationRef.current.setRobotState('idle');
      }
    };

    const handleVoiceCommandResult = (event: CustomEvent) => {
      const { userQuery, aiResponse } = event.detail;

      if (userQuery) {
        toast({
          title: 'Voice Command Received',
          description: `"${userQuery}"`,
        });
      }

      if (aiResponse) {
        // In fullscreen mode, show enhanced AI responses
        if (isFullScreenMode) {
          toast({
            title: 'Daswos AI Response',
            description: typeof aiResponse === 'string' ? aiResponse : 'AI response received',
            duration: 5000,
          });
        }
      }
    };

    const handleAISearch = (event: CustomEvent) => {
      const { query } = event.detail;
      if (query && isFullScreenMode) {
        // In fullscreen mode, show product display with search results
        setCurrentSearchQuery(query);
        setShowProductDisplay(true);

        toast({
          title: 'Searching Products',
          description: `Finding: "${query}"`,
          duration: 3000,
        });
      }
    };

    const handleAIAutoshop = () => {
      if (isFullScreenMode) {
        // Show product display with general recommendations
        setCurrentSearchQuery('recommended products');
        setShowProductDisplay(true);

        toast({
          title: 'AutoShop Activated',
          description: 'Starting intelligent shopping assistant...',
          duration: 3000,
        });
      }
    };

    // Add event listeners
    window.addEventListener('voiceStatus', handleVoiceStatus as EventListener);
    window.addEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
    window.addEventListener('aiSearch', handleAISearch as EventListener);
    window.addEventListener('aiAutoshop', handleAIAutoshop as EventListener);

    return () => {
      window.removeEventListener('voiceStatus', handleVoiceStatus as EventListener);
      window.removeEventListener('voiceCommandResult', handleVoiceCommandResult as EventListener);
      window.removeEventListener('aiSearch', handleAISearch as EventListener);
      window.removeEventListener('aiAutoshop', handleAIAutoshop as EventListener);
    };
  }, [isFullScreenMode, toast]);

  return (
    <div className={className}>
      {/* Show only the Daswos button when robot is not active */}
      {!isRobotActive && (
        <DaswosButton onClick={handleDaswosButtonClick} />
      )}

      {/* Show full robot interface when active */}
      {isRobotActive && (
        <>
          {/* Robot Animation Layer */}
          <RobotAnimation
            ref={robotAnimationRef}
            onRobotStateChange={handleRobotStateChange}
            isVisible={isRobotActive}
            isGlidingAway={isRobotGlidingAway}
            isFullScreenMode={isFullScreenMode}
          />

          {/* Robot Controls - hide when gliding away or when controls are closed */}
          {!isRobotGlidingAway && showRobotControls && (
            <RobotControls
              robotState={robotState}
              robotScale={robotScale}
              onStateChange={handleStateChange}
              onScaleChange={handleScaleChange}
              onCenter={handleCenter}
              onRoll={handleRoll}
              onClose={handleCloseRobotControls}
              onFullScreenToggle={handleFullScreenToggle}
              isFullScreenMode={isFullScreenMode}
            />
          )}

          {/* Go Away Button - show when robot is active but not gliding */}
          {!isRobotGlidingAway && (
            <GoAwayButton onClick={handleGoAway} />
          )}

          {/* Voice Control - active when robot is active */}
          {!isRobotGlidingAway && (
            <VoiceControlManager
              isActive={isVoiceListening}
              isFullScreen={isFullScreenMode}
              onVoiceCommand={handleVoiceCommand}
            />
          )}

          {/* Voice Status Display - show in fullscreen mode */}
          {isFullScreenMode && voiceStatus && (
            <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-black/80 text-white px-4 py-2 rounded-lg backdrop-blur-sm">
              {voiceStatus}
            </div>
          )}
        </>
      )}

      {/* Fullscreen Product Display - shows when voice commands trigger product searches */}
      <FullscreenProductDisplay
        isVisible={showProductDisplay && isFullScreenMode}
        searchQuery={currentSearchQuery}
        onClose={handleCloseProductDisplay}
      />
    </div>
  );
};

export default RobotOverlay;

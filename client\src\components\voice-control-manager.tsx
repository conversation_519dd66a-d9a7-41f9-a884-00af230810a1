import React, { useEffect, useRef, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';

interface VoiceControlManagerProps {
  isActive: boolean;
  isFullScreen: boolean;
  onVoiceCommand?: (command: string) => void;
}

const VoiceControlManager: React.FC<VoiceControlManagerProps> = ({
  isActive,
  isFullScreen,
  onVoiceCommand
}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const recognitionRef = useRef<any>(null);
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  // Initialize speech recognition when component becomes active
  useEffect(() => {
    console.log('🎤 VoiceControlManager useEffect:', { isActive });
    if (isActive) {
      console.log('🎤 Starting voice listening...');
      startListening();
    } else {
      console.log('🎤 Stopping voice listening...');
      stopListening();
    }

    return () => {
      stopListening();
    };
  }, [isActive]);

  const startListening = async () => {
    console.log('🎤 VoiceControlManager: Starting voice recognition...');

    // Check if already listening
    if (isListening || recognitionRef.current) {
      console.log('⚠️ Speech recognition already active, skipping start');
      return;
    }

    // Check if speech recognition is supported
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      const errorMsg = 'Speech recognition not supported in this browser';
      setError(errorMsg);
      toast({
        title: 'Voice Recognition Error',
        description: errorMsg,
        variant: 'destructive',
      });
      return;
    }

    // Request microphone permission first
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('✅ Microphone permission granted');
    } catch (permissionError) {
      const errorMsg = 'Microphone access denied. Please allow microphone access to use voice commands.';
      setError(errorMsg);
      toast({
        title: 'Microphone Permission Required',
        description: errorMsg,
        variant: 'destructive',
      });
      return;
    }

    try {
      const recognition = new SpeechRecognition();
      recognitionRef.current = recognition;

      // Configure recognition
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      let finalTranscript = '';
      let hasDetectedSpeech = false;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition started');
        setIsListening(true);
        setError(null);
        setTranscript('Listening for "Daswos"...');

        // Emit voice status event
        const statusEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'listening', message: '🎤 Listening for "Daswos"...' }
        });
        window.dispatchEvent(statusEvent);

        toast({
          title: 'Voice Recognition Active',
          description: 'Say "Daswos" followed by your command',
        });
      };

      recognition.onresult = (event: any) => {
        console.log('📝 Speech recognition result:', event);
        let interim = '';
        let final = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final += transcript;
          } else {
            interim += transcript;
          }
        }

        if (interim) {
          setTranscript(`Hearing: "${interim}"`);
          hasDetectedSpeech = true;
        }

        if (final) {
          finalTranscript = final;
          console.log('✅ Final transcript received:', final);

          // Check for wake word
          const lowerTranscript = final.toLowerCase();
          const containsDaswos = lowerTranscript.includes('daswos') ||
                                lowerTranscript.includes('das wos');

          console.log('🔍 Wake word check:', { lowerTranscript, containsDaswos });

          if (containsDaswos) {
            console.log('✅ Wake word detected! Processing command:', final);
            setTranscript(`Command received: "${final}"`);

            // Emit processing status
            const statusEvent = new CustomEvent('voiceStatus', {
              detail: { status: 'processing', message: '🤖 Processing your request...' }
            });
            window.dispatchEvent(statusEvent);

            // Call the voice command handler
            if (onVoiceCommand) {
              console.log('📞 Calling onVoiceCommand with:', final);
              onVoiceCommand(final);
            }

            // Process with AI
            console.log('🤖 Starting processWithAI for:', final);
            processWithAI(final);
            recognition.stop();
          } else {
            // No wake word, continue listening
            setTranscript('Listening for "Daswos"... Say "Daswos" followed by your command.');
            console.log('⚠️ No wake word detected in:', final);

            // Restart listening after a short delay
            setTimeout(() => {
              if (recognitionRef.current === recognition && isActive && !isListening) {
                try {
                  recognition.start();
                } catch (restartError) {
                  console.error('❌ Failed to restart recognition:', restartError);
                  // If restart fails, try creating a new instance
                  setTimeout(() => {
                    if (isActive) {
                      stopListening();
                      startListening();
                    }
                  }, 500);
                }
              }
            }, 1000);
          }
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
        setIsListening(false);

        if (event.error !== 'no-speech') {
          toast({
            title: 'Speech Recognition Error',
            description: `Error: ${event.error}`,
            variant: 'destructive',
          });
        }
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        setIsListening(false);

        if (!hasDetectedSpeech && !finalTranscript) {
          setTranscript('Listening for "Daswos"... Speak clearly and say "Daswos" followed by your command.');

          // Restart listening if still active
          setTimeout(() => {
            if (isActive && recognitionRef.current === recognition && !isListening) {
              console.log('🔄 Restarting voice recognition...');
              try {
                recognition.start();
              } catch (restartError) {
                console.error('❌ Failed to restart recognition:', restartError);
                // If restart fails, create a new instance
                setTimeout(() => {
                  if (isActive) {
                    stopListening();
                    startListening();
                  }
                }, 500);
              }
            }
          }, 1500);
        }
      };

      try {
        recognition.start();
      } catch (startError: any) {
        console.error('❌ Failed to start recognition:', startError);
        if (startError.message?.includes('already started')) {
          console.log('⚠️ Recognition already started, stopping and retrying...');
          recognition.stop();
          setTimeout(() => {
            if (isActive && recognitionRef.current === recognition) {
              try {
                recognition.start();
              } catch (retryError) {
                console.error('❌ Retry failed:', retryError);
                setError('Failed to start voice recognition');
              }
            }
          }, 1000);
        } else {
          setError('Failed to start voice recognition');
          setIsListening(false);
        }
      }

    } catch (err: any) {
      console.error('❌ Failed to start speech recognition:', err);
      setError('Failed to start speech recognition');
      setIsListening(false);

      toast({
        title: 'Speech Recognition Error',
        description: 'Failed to start speech recognition. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const stopListening = () => {
    console.log('🛑 VoiceControlManager: Stopping voice recognition...');

    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (stopError) {
        console.error('❌ Error stopping recognition:', stopError);
      }
      recognitionRef.current = null;
    }

    setIsListening(false);
    setTranscript('');
    setError(null);

    // Emit idle status
    const statusEvent = new CustomEvent('voiceStatus', {
      detail: { status: 'idle', message: '' }
    });
    window.dispatchEvent(statusEvent);
  };

  // Extract search query from voice command
  const extractSearchQuery = (command: string): string | null => {
    const searchPatterns = [
      /(?:daswos\s+)?(?:search for|find|look for|show me|get me)\s+(.+)/i,
      /(?:daswos\s+)?(?:i need|i want|i'm looking for)\s+(.+)/i,
      /(?:daswos\s+)(.+)/i // Fallback: anything after "daswos"
    ];

    for (const pattern of searchPatterns) {
      const match = command.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    return null;
  };

  // Handle navigation commands
  const handleNavigationCommand = (command: string): boolean => {
    const navigationPatterns = [
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?profile/i, route: '/profile' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?dashboard/i, route: '/dashboard' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:auto\s*shop|autoshop)/i, route: '/autoshop-dashboard' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:shopping\s+)?cart/i, route: '/cart' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?orders/i, route: '/orders' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:daswos\s+)?coins/i, route: '/daswos-coins' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?home(?:\s+page)?/i, route: '/' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?search(?:\s+page)?/i, route: '/search' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?shopping(?:\s+page)?/i, route: '/shopping-engine' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+settings/i, route: '/settings' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+help/i, route: '/help' },
    ];

    for (const { pattern, route } of navigationPatterns) {
      if (pattern.test(command)) {
        console.log(`🧭 Navigating to: ${route}`);
        setLocation(route);
        toast({
          title: 'Navigation Command',
          description: `Navigating to ${route}`,
        });
        return true;
      }
    }
    return false;
  };

  // Handle search commands directly
  const handleSearchCommand = (command: string): boolean => {
    console.log(`🔍 Attempting to extract search query from: "${command}"`);
    const searchQuery = extractSearchQuery(command);
    if (searchQuery) {
      console.log(`🔍 Search query extracted: "${searchQuery}"`);
      console.log(`🔍 Dispatching voiceSearch event with query: "${searchQuery}"`);

      // Update search bar by dispatching a custom event
      const searchEvent = new CustomEvent('voiceSearch', {
        detail: { query: searchQuery }
      });
      window.dispatchEvent(searchEvent);

      // Navigate to search page with query
      const encodedQuery = encodeURIComponent(searchQuery);
      console.log(`🔍 Navigating to: /search?q=${encodedQuery}`);
      setLocation(`/search?q=${encodedQuery}`);

      toast({
        title: 'Search Command',
        description: `Searching for: "${searchQuery}"`,
      });
      return true;
    } else {
      console.log(`🔍 No search query found in: "${command}"`);
    }
    return false;
  };

  const processWithAI = async (userQuery: string) => {
    console.log('🤖 Processing with AI:', userQuery);

    // First try to handle navigation commands directly
    if (handleNavigationCommand(userQuery)) {
      return;
    }

    // Then try to handle search commands directly
    if (handleSearchCommand(userQuery)) {
      return;
    }

    try {
      // Send to backend for processing
      const response = await fetch('/api/ai-conversation/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userQuery,
          conversationHistory: []
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ AI response received:', data);

      // Dispatch voice command result
      const voiceResultEvent = new CustomEvent('voiceCommandResult', {
        detail: {
          userQuery: userQuery,
          aiResponse: data.response,
          audio: null
        }
      });
      window.dispatchEvent(voiceResultEvent);

      // Handle specific AI actions
      if (data.response?.intent === 'search' && data.response?.parameters?.query) {
        const searchEvent = new CustomEvent('aiSearch', {
          detail: { query: data.response.parameters.query }
        });
        window.dispatchEvent(searchEvent);
      } else if (data.response?.intent === 'autoshop') {
        const autoshopEvent = new CustomEvent('aiAutoshop');
        window.dispatchEvent(autoshopEvent);
      }

      toast({
        title: 'Voice Command Processed',
        description: `"${userQuery}" → Command executed`,
      });

    } catch (error) {
      console.error('❌ AI processing error:', error);
      toast({
        title: 'Processing Error',
        description: 'Failed to process voice command. Please try again.',
        variant: 'destructive',
      });
    }

    // Emit idle status
    const idleEvent = new CustomEvent('voiceStatus', {
      detail: { status: 'idle', message: '' }
    });
    window.dispatchEvent(idleEvent);
  };

  // Test function to manually trigger voice search
  const testVoiceSearch = () => {
    console.log('🧪 Testing voice search manually');
    const testQuery = 'headphones';
    handleSearchCommand(`daswos search for ${testQuery}`);
  };

  // Render voice status indicator
  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isListening && (
        <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            {transcript || 'Listening...'}
          </span>
        </div>
      )}

      {error && (
        <div className="bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg mt-2">
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* Debug test button - remove in production */}
      {isActive && (
        <button
          onClick={testVoiceSearch}
          className="bg-green-600 text-white px-3 py-1 rounded text-xs mt-2 hover:bg-green-700"
        >
          Test Voice Search
        </button>
      )}
    </div>
  );
};

export default VoiceControlManager;

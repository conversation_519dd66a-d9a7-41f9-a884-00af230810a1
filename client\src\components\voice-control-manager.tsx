import React, { useEffect, useRef, useState } from 'react';
import { useToast } from '@/hooks/use-toast';

interface VoiceControlManagerProps {
  isActive: boolean;
  isFullScreen: boolean;
  onVoiceCommand?: (command: string) => void;
}

const VoiceControlManager: React.FC<VoiceControlManagerProps> = ({
  isActive,
  isFullScreen,
  onVoiceCommand
}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const recognitionRef = useRef<any>(null);
  const { toast } = useToast();

  // Initialize speech recognition when component becomes active
  useEffect(() => {
    if (isActive) {
      startListening();
    } else {
      stopListening();
    }

    return () => {
      stopListening();
    };
  }, [isActive]);

  const startListening = async () => {
    console.log('🎤 VoiceControlManager: Starting voice recognition...');

    // Check if already listening
    if (isListening || recognitionRef.current) {
      console.log('⚠️ Speech recognition already active, skipping start');
      return;
    }

    // Check if speech recognition is supported
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      const errorMsg = 'Speech recognition not supported in this browser';
      setError(errorMsg);
      toast({
        title: 'Voice Recognition Error',
        description: errorMsg,
        variant: 'destructive',
      });
      return;
    }

    // Request microphone permission first
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('✅ Microphone permission granted');
    } catch (permissionError) {
      const errorMsg = 'Microphone access denied. Please allow microphone access to use voice commands.';
      setError(errorMsg);
      toast({
        title: 'Microphone Permission Required',
        description: errorMsg,
        variant: 'destructive',
      });
      return;
    }

    try {
      const recognition = new SpeechRecognition();
      recognitionRef.current = recognition;

      // Configure recognition
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      let finalTranscript = '';
      let hasDetectedSpeech = false;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition started');
        setIsListening(true);
        setError(null);
        setTranscript('Listening for "Daswos"...');

        // Emit voice status event
        const statusEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'listening', message: '🎤 Listening for "Daswos"...' }
        });
        window.dispatchEvent(statusEvent);

        toast({
          title: 'Voice Recognition Active',
          description: 'Say "Daswos" followed by your command',
        });
      };

      recognition.onresult = (event: any) => {
        console.log('📝 Speech recognition result:', event);
        let interim = '';
        let final = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final += transcript;
          } else {
            interim += transcript;
          }
        }

        if (interim) {
          setTranscript(`Hearing: "${interim}"`);
          hasDetectedSpeech = true;
        }

        if (final) {
          finalTranscript = final;
          console.log('✅ Final transcript:', final);

          // Check for wake word
          const lowerTranscript = final.toLowerCase();
          const containsDaswos = lowerTranscript.includes('daswos') || 
                                lowerTranscript.includes('das wos');

          if (containsDaswos) {
            setTranscript(`Command received: "${final}"`);
            
            // Emit processing status
            const statusEvent = new CustomEvent('voiceStatus', {
              detail: { status: 'processing', message: '🤖 Processing your request...' }
            });
            window.dispatchEvent(statusEvent);

            // Call the voice command handler
            if (onVoiceCommand) {
              onVoiceCommand(final);
            }

            // Process with AI
            processWithAI(final);
            recognition.stop();
          } else {
            // No wake word, continue listening
            setTranscript('Listening for "Daswos"... Say "Daswos" followed by your command.');
            console.log('⚠️ No wake word detected in:', final);
            
            // Restart listening after a short delay
            setTimeout(() => {
              if (recognitionRef.current === recognition && isActive && !isListening) {
                try {
                  recognition.start();
                } catch (restartError) {
                  console.error('❌ Failed to restart recognition:', restartError);
                  // If restart fails, try creating a new instance
                  setTimeout(() => {
                    if (isActive) {
                      stopListening();
                      startListening();
                    }
                  }, 500);
                }
              }
            }, 1000);
          }
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
        setIsListening(false);

        if (event.error !== 'no-speech') {
          toast({
            title: 'Speech Recognition Error',
            description: `Error: ${event.error}`,
            variant: 'destructive',
          });
        }
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        setIsListening(false);

        if (!hasDetectedSpeech && !finalTranscript) {
          setTranscript('Listening for "Daswos"... Speak clearly and say "Daswos" followed by your command.');

          // Restart listening if still active
          setTimeout(() => {
            if (isActive && recognitionRef.current === recognition && !isListening) {
              console.log('🔄 Restarting voice recognition...');
              try {
                recognition.start();
              } catch (restartError) {
                console.error('❌ Failed to restart recognition:', restartError);
                // If restart fails, create a new instance
                setTimeout(() => {
                  if (isActive) {
                    stopListening();
                    startListening();
                  }
                }, 500);
              }
            }
          }, 1500);
        }
      };

      try {
        recognition.start();
      } catch (startError: any) {
        console.error('❌ Failed to start recognition:', startError);
        if (startError.message?.includes('already started')) {
          console.log('⚠️ Recognition already started, stopping and retrying...');
          recognition.stop();
          setTimeout(() => {
            if (isActive && recognitionRef.current === recognition) {
              try {
                recognition.start();
              } catch (retryError) {
                console.error('❌ Retry failed:', retryError);
                setError('Failed to start voice recognition');
              }
            }
          }, 1000);
        } else {
          setError('Failed to start voice recognition');
          setIsListening(false);
        }
      }

    } catch (err: any) {
      console.error('❌ Failed to start speech recognition:', err);
      setError('Failed to start speech recognition');
      setIsListening(false);

      toast({
        title: 'Speech Recognition Error',
        description: 'Failed to start speech recognition. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const stopListening = () => {
    console.log('🛑 VoiceControlManager: Stopping voice recognition...');

    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (stopError) {
        console.error('❌ Error stopping recognition:', stopError);
      }
      recognitionRef.current = null;
    }

    setIsListening(false);
    setTranscript('');
    setError(null);

    // Emit idle status
    const statusEvent = new CustomEvent('voiceStatus', {
      detail: { status: 'idle', message: '' }
    });
    window.dispatchEvent(statusEvent);
  };

  const processWithAI = async (userQuery: string) => {
    console.log('🤖 Processing with AI:', userQuery);

    try {
      // Send to backend for processing
      const response = await fetch('/api/ai-conversation/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userQuery,
          conversationHistory: []
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ AI response received:', data);

      // Dispatch voice command result
      const voiceResultEvent = new CustomEvent('voiceCommandResult', {
        detail: {
          userQuery: userQuery,
          aiResponse: data.response,
          audio: null
        }
      });
      window.dispatchEvent(voiceResultEvent);

      // Handle specific AI actions
      if (data.response?.intent === 'search' && data.response?.parameters?.query) {
        const searchEvent = new CustomEvent('aiSearch', {
          detail: { query: data.response.parameters.query }
        });
        window.dispatchEvent(searchEvent);
      } else if (data.response?.intent === 'autoshop') {
        const autoshopEvent = new CustomEvent('aiAutoshop');
        window.dispatchEvent(autoshopEvent);
      }

      toast({
        title: 'Voice Command Processed',
        description: `"${userQuery}" → Command executed`,
      });

    } catch (error) {
      console.error('❌ AI processing error:', error);
      toast({
        title: 'Processing Error',
        description: 'Failed to process voice command. Please try again.',
        variant: 'destructive',
      });
    }

    // Emit idle status
    const idleEvent = new CustomEvent('voiceStatus', {
      detail: { status: 'idle', message: '' }
    });
    window.dispatchEvent(idleEvent);
  };

  // Render voice status indicator
  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isListening && (
        <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            {transcript || 'Listening...'}
          </span>
        </div>
      )}
      
      {error && (
        <div className="bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg mt-2">
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  );
};

export default VoiceControlManager;

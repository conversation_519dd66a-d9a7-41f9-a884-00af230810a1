import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

const VoiceDebugPanel: React.FC = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [audioLevel, setAudioLevel] = useState(0);
  const [permissionStatus, setPermissionStatus] = useState<'unknown' | 'granted' | 'denied'>('unknown');
  const [browserSupport, setBrowserSupport] = useState<string>('');
  
  const recognitionRef = useRef<any>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const { toast } = useToast();

  useEffect(() => {
    // Check browser support
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (SpeechRecognition) {
      setBrowserSupport('✅ Speech Recognition supported');
    } else {
      setBrowserSupport('❌ Speech Recognition not supported');
    }

    // Check microphone permission
    navigator.permissions?.query({ name: 'microphone' as PermissionName })
      .then(result => {
        setPermissionStatus(result.state as any);
        result.onchange = () => setPermissionStatus(result.state as any);
      })
      .catch(() => setPermissionStatus('unknown'));

    return () => {
      stopListening();
    };
  }, []);

  const startListening = async () => {
    console.log('🎤 Debug: Starting voice recognition...');
    
    // Request microphone permission
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      streamRef.current = stream;
      setPermissionStatus('granted');
      
      // Set up audio level monitoring
      setupAudioLevelMonitoring(stream);
      
    } catch (error) {
      console.error('❌ Microphone access denied:', error);
      setPermissionStatus('denied');
      toast({
        title: 'Microphone Access Denied',
        description: 'Please allow microphone access to use voice commands',
        variant: 'destructive',
      });
      return;
    }

    // Set up speech recognition
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      toast({
        title: 'Speech Recognition Not Supported',
        description: 'Your browser does not support speech recognition',
        variant: 'destructive',
      });
      return;
    }

    const recognition = new SpeechRecognition();
    recognitionRef.current = recognition;

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
      console.log('🎤 Speech recognition started');
      setIsListening(true);
      setTranscript('Listening...');
    };

    recognition.onresult = (event: any) => {
      console.log('📝 Speech result:', event);
      let interim = '';
      let final = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        const confidence = event.results[i][0].confidence;
        
        console.log(`Result ${i}:`, { transcript, confidence, isFinal: event.results[i].isFinal });
        
        if (event.results[i].isFinal) {
          final += transcript;
        } else {
          interim += transcript;
        }
      }

      if (interim) {
        setTranscript(`Interim: "${interim}"`);
      }

      if (final) {
        setTranscript(`Final: "${final}"`);
        console.log('✅ Final transcript:', final);
        
        // Check for wake word
        const lowerTranscript = final.toLowerCase();
        if (lowerTranscript.includes('daswos') || lowerTranscript.includes('das wos')) {
          toast({
            title: 'Wake Word Detected!',
            description: `Command: "${final}"`,
          });
        }
      }
    };

    recognition.onerror = (event: any) => {
      console.error('❌ Speech recognition error:', event.error);
      setTranscript(`Error: ${event.error}`);
      toast({
        title: 'Speech Recognition Error',
        description: event.error,
        variant: 'destructive',
      });
    };

    recognition.onend = () => {
      console.log('🛑 Speech recognition ended');
      setIsListening(false);
    };

    try {
      recognition.start();
    } catch (error) {
      console.error('❌ Failed to start recognition:', error);
      setIsListening(false);
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      recognitionRef.current = null;
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    setIsListening(false);
    setTranscript('');
    setAudioLevel(0);
  };

  const setupAudioLevelMonitoring = (stream: MediaStream) => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      microphone.connect(analyser);
      analyser.fftSize = 256;
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      const updateAudioLevel = () => {
        if (analyserRef.current && isListening) {
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / bufferLength;
          setAudioLevel(average);
          requestAnimationFrame(updateAudioLevel);
        }
      };
      
      updateAudioLevel();
    } catch (error) {
      console.error('❌ Failed to set up audio monitoring:', error);
    }
  };

  return (
    <div className="fixed top-4 left-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border max-w-md z-50">
      <h3 className="font-bold text-lg mb-3">Voice Recognition Debug</h3>
      
      <div className="space-y-2 text-sm">
        <div>Browser Support: {browserSupport}</div>
        <div>Microphone Permission: {permissionStatus}</div>
        <div>Audio Level: {audioLevel.toFixed(1)}</div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-100" 
            style={{ width: `${Math.min(audioLevel * 2, 100)}%` }}
          />
        </div>
      </div>
      
      <div className="mt-3">
        <div className="text-sm font-medium mb-1">Transcript:</div>
        <div className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded min-h-[40px]">
          {transcript || 'No speech detected'}
        </div>
      </div>
      
      <div className="flex gap-2 mt-3">
        <Button
          onClick={isListening ? stopListening : startListening}
          variant={isListening ? "destructive" : "default"}
          size="sm"
        >
          {isListening ? 'Stop' : 'Start'} Listening
        </Button>
      </div>
      
      <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
        Say "Daswos search for headphones" to test
      </div>
    </div>
  );
};

export default VoiceDebugPanel;

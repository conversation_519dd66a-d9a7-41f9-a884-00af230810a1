import React, { createContext, useContext, useState, ReactNode } from 'react';

interface RobotContextType {
  isRobotFullScreen: boolean;
  setIsRobotFullScreen: (fullScreen: boolean) => void;
}

const RobotContext = createContext<RobotContextType | undefined>(undefined);

export function useRobotContext() {
  const context = useContext(RobotContext);
  if (!context) {
    throw new Error('useRobotContext must be used within a RobotProvider');
  }
  return context;
}

interface RobotProviderProps {
  children: ReactNode;
}

export function RobotProvider({ children }: RobotProviderProps) {
  const [isRobotFullScreen, setIsRobotFullScreen] = useState(false);

  return (
    <RobotContext.Provider value={{ isRobotFullScreen, setIsRobotFullScreen }}>
      {children}
    </RobotContext.Provider>
  );
}
